// ==================== AUTHENTICATION TYPES ====================

// POST /vendor-platform/auth/login
export interface LoginResponse {
  message: string;
  data: {
    token: string;
    user: {
      _id: string;
      email: string;
      name: string;
      userType: string;
      organizationId?: string;
    };
  };
}

// ==================== USER ROLES & PERMISSIONS TYPES ====================

// GET /vendor-platform/user-roles-permissions/me
export interface MyPermissionsResponse {
  message: string;
  data: {
    _id: string;
    userId: string;
    userType: 'ocn' | 'organization' | 'workshop' | 'company' | 'company-gestor' | 'all';
    organizationId?: string;
    roles: string[];
    permissions: {
      fleetOrders: {
        create: boolean;
        read: boolean;
        update: boolean;
        delete: boolean;
        manageAll: boolean;
        viewAllOrders: boolean;
        manageDispersion: boolean;
        uploadEvidence: boolean;
        manageSLAs: boolean;
      };
      organizations: {
        create: boolean;
        read: boolean;
        update: boolean;
        delete: boolean;
        manageAll: boolean;
        viewAllOrganizations: boolean;
        manageUsers: boolean;
      };
      users: {
        create: boolean;
        read: boolean;
        update: boolean;
        delete: boolean;
        manageAll: boolean;
        viewAllUsers: boolean;
        manageRoles: boolean;
        inviteUsers: boolean;
      };
      systemAdmin: {
        viewSystemLogs: boolean;
        manageSystemSettings: boolean;
        viewAnalytics: boolean;
        exportData: boolean;
      };
    };
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

// POST /vendor-platform/user-roles-permissions
export interface CreateUserPermissionsResponse {
  message: string;
  data: {
    _id: string;
    userId: string;
    userType: string;
    organizationId?: string;
    roles: string[];
    permissions: object;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

// ==================== FLEET ORDERS - MAIN OPERATIONS TYPES ====================

export interface VehicleOrder {
  brand: string;
  dealer: string;
  model: string;
  version: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
}

export interface StatusHistory {
  status: string;
  timestamp: string;
  userId: string;
  evidence?: {
    type: 'log' | 'photo' | 'pdf' | 'document';
    url?: string;
    description: string;
  };
  notes?: string;
}

export interface Dispersion {
  state: string;
  city: string;
  quantity: number;
  deliveryDate: string;
  amount: number;
}

export interface FleetOrder {
  _id: string;
  orderNumber: string;
  month: number;
  year: number;
  status: 'created' | 'sent' | 'dispersion' | 'invoice_letter_request' | 'invoice_letter_arrival' | 'supplier_notification' | 'waiting_for_cars' | 'delivered';
  vehicles: VehicleOrder[];
  totalUnits: number;
  totalAmount: number;
  notificationEmails: string[];
  sentDeadline: string;
  dispersionDeadline: string;
  invoiceLetterRequestDeadline: string;
  invoiceLetterArrivalDeadline: string;
  statusHistory: StatusHistory[];
  dispersion?: Dispersion[];
  invoiceLetters?: {
    requestedAt: string;
    arrivedAt?: string;
    evidence?: {
      type: 'photo' | 'pdf';
      url: string;
    };
  };
  supplierNotifications?: {
    sentAt: string;
    evidence: {
      description: string;
      timestamp: string;
    };
  };
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
}

// POST /vendor-platform/fleet-orders
export interface CreateFleetOrderResponse {
  message: string;
  data: FleetOrder;
}

// GET /vendor-platform/fleet-orders
export interface ListFleetOrdersResponse {
  message: string;
  data: FleetOrder[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// GET /vendor-platform/fleet-orders/:id
export interface GetFleetOrderResponse {
  message: string;
  data: FleetOrder;
}

// GET /vendor-platform/fleet-orders/number/:orderNumber
export interface GetFleetOrderByNumberResponse {
  message: string;
  data: FleetOrder;
}

// PATCH /vendor-platform/fleet-orders/:id/status
export interface UpdateFleetOrderStatusResponse {
  message: string;
  data: FleetOrder;
}

// PATCH /vendor-platform/fleet-orders/:id/dispersion
export interface UpdateDispersionResponse {
  message: string;
  data: FleetOrder;
}

// DELETE /vendor-platform/fleet-orders/:id
export interface DeleteFleetOrderResponse {
  message: string;
}

// ==================== SLA & ALERTS TYPES ====================

export interface SLAAlert {
  _id: string;
  orderId: string;
  orderNumber: string;
  alertType: 'sla_warning' | 'sla_exceeded';
  status: string;
  deadline: string;
  daysRemaining: number;
  isResolved: boolean;
  sentToSlack: boolean;
  slackMessageId?: string;
  createdAt: string;
  updatedAt: string;
  order?: {
    orderNumber: string;
    month: number;
    year: number;
    status: string;
    totalUnits: number;
    totalAmount: number;
  };
}

// GET /vendor-platform/fleet-orders/sla/alerts
export interface GetSLAAlertsResponse {
  message: string;
  data: SLAAlert[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// GET /vendor-platform/fleet-orders/:orderId/alerts
export interface GetAlertsForOrderResponse {
  message: string;
  data: SLAAlert[];
}

// POST /vendor-platform/fleet-orders/sla/check
export interface RunSLACheckResponse {
  message: string;
  data: {
    newAlertsCount: number;
    newAlerts: SLAAlert[];
  };
}

// GET /vendor-platform/fleet-orders/sla/statistics
export interface GetSLAStatisticsResponse {
  message: string;
  data: {
    totalActiveOrders: number;
    ordersOnTime: number;
    ordersWithWarnings: number;
    ordersExceeded: number;
    alertsByStatus: {
      [status: string]: number;
    };
  };
}

// GET /vendor-platform/fleet-orders/sla/daily-summary
export interface GetDailySummaryResponse {
  message: string;
  data: {
    date: string;
    totalOrders: number;
    newWarnings: number;
    newExceeded: number;
    resolvedToday: number;
    criticalOrders: Array<{
      _id: string;
      orderNumber: string;
      order: {
        orderNumber: string;
        month: number;
        year: number;
        status: string;
      };
    }>;
  };
}

// PATCH /vendor-platform/fleet-orders/sla/alerts/:alertId/resolve
export interface ResolveAlertResponse {
  message: string;
  data: SLAAlert;
}

// PATCH /vendor-platform/fleet-orders/:orderId/alerts/resolve-all
export interface ResolveAllAlertsResponse {
  message: string;
  data: {
    resolvedCount: number;
  };
}

// ==================== SLACK INTEGRATION TYPES ====================

// POST /vendor-platform/fleet-orders/slack/daily-summary
export interface SendDailySummaryToSlackResponse {
  message: string;
  data: {
    summary: {
      date: string;
      totalOrders: number;
      newWarnings: number;
      newExceeded: number;
      resolvedToday: number;
      criticalOrders: any[];
    };
    slackMessageId: string;
  };
}

// POST /vendor-platform/fleet-orders/slack/send-pending-alerts
export interface SendPendingAlertsToSlackResponse {
  message: string;
  data: {
    totalPending: number;
    sentCount: number;
    failedCount: number;
  };
}

// ==================== MAINTENANCE TYPES ====================

// DELETE /vendor-platform/fleet-orders/sla/cleanup
export interface CleanupOldAlertsResponse {
  message: string;
  data: {
    deletedCount: number;
  };
}

// ==================== ERROR TYPES ====================

export interface ErrorResponse {
  message: string;
  code: string;
  errors?: string[];
  data?: any;
}

// ==================== REQUEST BODY TYPES ====================

export interface CreateFleetOrderRequest {
  month: number;
  year: number;
  vehicles: Array<{
    brand: string;
    dealer: string;
    model: string;
    version: string;
    quantity: number;
    unitPrice: number;
  }>;
  notificationEmails: string[];
}

export interface UpdateFleetOrderStatusRequest {
  status: 'created' | 'sent' | 'dispersion' | 'invoice_letter_request' | 'invoice_letter_arrival' | 'supplier_notification' | 'waiting_for_cars' | 'delivered';
  evidence?: {
    type: 'log' | 'photo' | 'pdf' | 'document';
    url?: string;
    description: string;
  };
  notes?: string;
}

export interface UpdateDispersionRequest {
  dispersion: Array<{
    state: string;
    city: string;
    quantity: number;
    deliveryDate: string;
    amount: number;
  }>;
}

export interface CreateUserPermissionsRequest {
  userId: string;
  userType: 'ocn' | 'organization' | 'workshop' | 'company' | 'company-gestor' | 'all';
  organizationId?: string;
  roles: string[];
}
