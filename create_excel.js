const XLSX = require('xlsx');

// Datos para la columna Contrato
const contratos = [
    12144,
    12145,
    12143,
    12140,
    12139,
    12138,
    12146,
    12142,
    12141,
    12147,
    12132,
    12133,
    12134,
    12135,
    12136,
    12137,
    12126,
    12127,
    12128,
    12129,
    12130,
    12131
];

// Crear los datos en formato de array de objetos
const data = contratos.map(contrato => ({ Contrato: contrato }));

// Crear un nuevo workbook
const wb = XLSX.utils.book_new();

// Crear una worksheet con los datos
const ws = XLSX.utils.json_to_sheet(data);

// Agregar la worksheet al workbook
XLSX.utils.book_append_sheet(wb, ws, 'Contratos');

// Escribir el archivo
XLSX.writeFile(wb, 'data.xlsx');

console.log('Archivo data.xlsx creado exitosamente con', contratos.length, 'contratos');
